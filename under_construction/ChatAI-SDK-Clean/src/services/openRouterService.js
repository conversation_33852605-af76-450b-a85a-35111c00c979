const fetch = require('node-fetch');
const config = require('../config');

class OpenRouterService {
  constructor() {
    this.apiKey = config.openRouter.apiKey;
    this.baseUrl = config.openRouter.baseUrl;
    this.model = config.openRouter.model;
    this.isConfigured = !!this.apiKey;

    if (!this.isConfigured) {
      console.warn('⚠️  OPENROUTER_API_KEY not configured. Chat functionality will be disabled.');
    } else {
      console.log('✅ OpenRouterService initialized successfully');
    }
  }

  checkConfiguration() {
    if (!this.isConfigured) {
      throw new Error('OpenRouter service is not configured. Please set OPENROUTER_API_KEY.');
    }
  }

  /**
   * Generate chat response with context
   * @param {string} query - User query
   * @param {string} context - Retrieved context from documents
   * @param {Array} chatHistory - Previous chat messages (optional)
   * @returns {Promise<string>} Generated response
   */
  async generateResponse(query, context = '', chatHistory = []) {
    this.checkConfiguration();

    try {
      console.log(`🤖 Generating response for query: "${query.substring(0, 50)}..."`);

      const systemPrompt = this.buildSystemPrompt(context);
      const messages = this.buildMessages(systemPrompt, query, chatHistory);

      const payload = {
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: false
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chatai-sdk.com',
          'X-Title': 'ChatAI SDK Service'
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ OpenRouter error: ${response.status} - ${errorText}`);
        throw new Error(`OpenRouter API failed: ${response.status}`);
      }

      const result = await response.json();
      const generatedResponse = result.choices?.[0]?.message?.content || 'No response generated';

      console.log(`✅ Generated response (${generatedResponse.length} chars)`);
      return generatedResponse;

    } catch (error) {
      console.error(`❌ OpenRouter generateResponse error:`, error.message);
      throw error;
    }
  }

  /**
   * Generate streaming chat response
   * @param {string} query - User query
   * @param {string} context - Retrieved context from documents
   * @param {Array} chatHistory - Previous chat messages (optional)
   * @returns {AsyncGenerator<string>} Streaming response chunks
   */
  async* generateStreamingResponse(query, context = '', chatHistory = []) {
    this.checkConfiguration();

    try {
      console.log(`🤖 Generating streaming response for query: "${query.substring(0, 50)}..."`);

      const systemPrompt = this.buildSystemPrompt(context);
      const messages = this.buildMessages(systemPrompt, query, chatHistory);

      const payload = {
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: true
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chatai-sdk.com',
          'X-Title': 'ChatAI SDK Service'
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API failed: ${response.status} - ${errorText}`);
      }

      // Parse streaming response using Node.js streams
      let buffer = '';

      for await (const chunk of response.body) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');

        // Keep the last incomplete line in buffer
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim().startsWith('data: ')) {
            const data = line.trim().slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch (parseError) {
              // Skip invalid JSON
            }
          }
        }
      }

    } catch (error) {
      console.error(`❌ OpenRouter streaming error:`, error.message);
      throw error;
    }
  }

  /**
   * Build system prompt with context
   * @param {string} context - Retrieved context from documents
   * @returns {string} System prompt
   */
  buildSystemPrompt(context) {
    if (!context || context.trim() === '') {
      return `You are a helpful AI assistant. Please provide accurate and helpful responses to user questions.`;
    }

    return `You are a helpful AI assistant with access to relevant document context. 

CONTEXT FROM DOCUMENTS:
${context}

Instructions:
1. Use the provided context to answer user questions accurately
2. If the context doesn't contain relevant information, say so clearly
3. Cite specific documents when referencing information
4. Be concise but comprehensive in your responses
5. If asked about something not in the context, provide general knowledge but clarify the source`;
  }

  /**
   * Build messages array for chat completion
   * @param {string} systemPrompt - System prompt
   * @param {string} query - User query
   * @param {Array} chatHistory - Previous chat messages
   * @returns {Array} Messages array
   */
  buildMessages(systemPrompt, query, chatHistory = []) {
    const messages = [
      { role: 'system', content: systemPrompt }
    ];

    // Add chat history (last 10 messages to stay within token limits)
    const recentHistory = chatHistory.slice(-10);
    messages.push(...recentHistory);

    // Add current user query
    messages.push({ role: 'user', content: query });

    return messages;
  }
}

module.exports = new OpenRouterService();
